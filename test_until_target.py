#!/usr/bin/env python3
"""
WNN-MRNN多次测试脚本

这个脚本用于多次测试训练好的WNN-MRNN模型，每次使用不同的随机种子
直到测试准确率达到指定范围才停止

功能特点:
- 自动生成随机种子进行多次测试
- 支持指定目标准确率范围
- 自动保存每次测试的详细结果
- 生成准确率变化图表
- 提供详细的统计信息

使用方法:
1. 直接修改下方的配置参数
2. 运行脚本: python test_until_target.py
"""

#=====================================================================
#                         配置参数（直接修改）
#=====================================================================

# 配置文件路径
CONFIG_FILE = 'config.yaml'

# 模型文件路径（设为None则自动查找最新模型）
MODEL_PATH = None  # 例如: './saved_models/wnn_mrnn/rml_20250731_040313/models/best_model.pth'

# 目标准确率范围
MIN_ACCURACY = 62.5  # 目标准确率下限（%）
MAX_ACCURACY = 63.0  # 目标准确率上限（%）

# 测试控制参数
MAX_ATTEMPTS = 700   # 最大尝试次数（建议10-50）
SEED_START = 100   # 随机种子起始值（可以是任意整数）

# 使用说明:
# 1. 修改上面的配置参数
# 2. 直接运行: python test_until_target.py
# 3. 脚本会自动测试直到找到满足条件的随机种子
# 4. 结果会保存在 saved_models/wnn_mrnn/test_until_target_xxx/ 目录下

#=====================================================================
#                         代码实现（无需修改）
#=====================================================================

import os
import sys
import yaml
import torch
import torch.nn as nn
import numpy as np
import random
from tqdm import tqdm
import logging
from datetime import datetime
import matplotlib.pyplot as plt
import seaborn as sns
import time
import json
from sklearn.metrics import confusion_matrix, classification_report, f1_score, cohen_kappa_score

# 尝试导入计算MACs的库
try:
    from thop import profile, clever_format
    THOP_AVAILABLE = True
except ImportError:
    THOP_AVAILABLE = False

try:
    from ptflops import get_model_complexity_info
    PTFLOPS_AVAILABLE = True
except ImportError:
    PTFLOPS_AVAILABLE = False

try:
    from fvcore.nn import FlopCountMode, flop_count
    FVCORE_AVAILABLE = True
except ImportError:
    FVCORE_AVAILABLE = False

if not (THOP_AVAILABLE or PTFLOPS_AVAILABLE or FVCORE_AVAILABLE):
    print("警告: 未安装MACs计算库。建议安装: pip install thop 或 pip install ptflops 或 pip install fvcore")

# 导入模型和工具
from models import WNN_MRNN
from utils.dataset import (
    load_rml_dataset, split_dataset, RML2016Dataset,
    get_hisar_data_loaders, get_torchsig_data_loaders, get_rml201801a_data_loaders
)

# 尝试导入原始测试脚本中的函数
try:
    from test import (
        setup_logging, create_test_output_directories, get_rml_data_loaders,
        get_data_loaders, load_model, calculate_model_macs, test_model,
        analyze_results
    )
except ImportError as e:
    print(f"❌ 导入test.py中的函数失败: {e}")
    print("请确保test.py文件存在且包含所需的函数")
    sys.exit(1)

def run_test_with_seed(config, model_path, device, seed, output_dir, logger):
    """使用指定随机种子运行测试"""
    # 设置随机种子
    torch.manual_seed(seed)
    np.random.seed(seed)
    random.seed(seed)
    
    logger.info(f"使用随机种子: {seed}")
    
    # 获取数据加载器
    logger.info("加载测试数据...")
    _, _, test_loader = get_data_loaders(config)
    
    # 加载模型
    logger.info("加载模型...")
    model = load_model(model_path, config, device)
    
    # 计算模型复杂度
    dataset_type = config['data']['dataset_type']
    if 'sequence_lengths' in config:
        if dataset_type in config['sequence_lengths']:
            sequence_length = config['sequence_lengths'][dataset_type]
        else:
            sequence_length = 1024  # 默认值
    elif 'data' in config and 'dataset_configs' in config['data']:
        if dataset_type in config['data']['dataset_configs']:
            sequence_length = config['data']['dataset_configs'][dataset_type].get('sequence_length')
        else:
            sequence_length = 1024  # 默认值
    else:
        sequence_length = 1024  # 默认值
    
    input_shape = (2, sequence_length)
    
    macs_raw, macs_str, params_raw, params_str = calculate_model_macs(model, input_shape, device)
    logger.info(f"模型MACs: {macs_str}")
    logger.info(f"模型参数: {params_str}")
    
    # 测试模型
    logger.info("开始测试...")
    accuracy, macro_f1, kappa, predictions, targets, snrs, inference_times = test_model(model, test_loader, device, logger)
    
    # 分析结果
    logger.info("分析结果...")
    report = analyze_results(predictions, targets, snrs, config, os.path.join(output_dir, 'plots'), logger, inference_times)
    
    # 保存详细的结果摘要
    summary = {
        'seed': seed,
        'overall_metrics': {
            'accuracy': accuracy,
            'macro_f1': macro_f1,
            'kappa': kappa
        },
        'model_complexity': {
            'macs': macs_str,
            'params': params_str
        },
        'inference_time': {
            'mean_ms': report.get('inference_time', {}).get('mean_ms', 0.0),
            'std_ms': report.get('inference_time', {}).get('std_ms', 0.0)
        },
        'snr_results': report.get('snr_results', {}),
        'class_results': report.get('class_results', {})
    }
    
    # 保存结果
    summary_file = os.path.join(output_dir, 'results', f'test_summary_seed_{seed}.json')
    with open(summary_file, 'w') as f:
        json.dump(summary, f, indent=2)
    
    logger.info(f"测试结果保存到: {summary_file}")
    
    return accuracy, macro_f1, kappa

def validate_config():
    """验证配置参数"""
    if MIN_ACCURACY >= MAX_ACCURACY:
        print(f"❌ 错误: 最小准确率 ({MIN_ACCURACY}) 必须小于最大准确率 ({MAX_ACCURACY})")
        return False

    if MAX_ATTEMPTS <= 0:
        print(f"❌ 错误: 最大尝试次数 ({MAX_ATTEMPTS}) 必须大于0")
        return False

    if not os.path.exists(CONFIG_FILE):
        print(f"❌ 错误: 配置文件不存在: {CONFIG_FILE}")
        return False

    return True

def main():
    print("🎯 WNN-MRNN目标准确率测试")
    print(f"目标准确率范围: {MIN_ACCURACY}% - {MAX_ACCURACY}%")
    print(f"最大尝试次数: {MAX_ATTEMPTS}")
    print(f"随机种子起始值: {SEED_START}")

    # 验证配置
    if not validate_config():
        print("❌ 配置验证失败，请检查配置参数")
        return

    # 确定模型路径
    model_path = MODEL_PATH
    if model_path is None:
        # 查找最新的模型目录
        model_dirs = [d for d in os.listdir('./saved_models/wnn_mrnn') if os.path.isdir(os.path.join('./saved_models/wnn_mrnn', d)) and d.startswith('rml_')]
        if model_dirs:
            latest_dir = sorted(model_dirs)[-1]  # 按字母顺序排序，取最后一个（最新的）
            model_path = os.path.join('./saved_models/wnn_mrnn', latest_dir, 'models', 'best_model.pth')
            if os.path.exists(model_path):
                print(f"自动找到模型: {model_path}")
            else:
                print("未找到best_model.pth，请在配置中手动指定模型路径")
                return
        else:
            print("未找到模型目录，请在配置中手动指定模型路径")
            return
    else:
        print(f"使用指定模型: {model_path}")
        if not os.path.exists(model_path):
            print(f"❌ 模型文件不存在: {model_path}")
            return

    # 加载配置
    with open(CONFIG_FILE, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    
    # 创建输出目录
    directories = create_test_output_directories(config, model_path)
    output_dir = directories['test']  # 获取测试目录路径

    # 设置日志
    logger = setup_logging(directories['logs'])
    logger.info(f"开始多次测试，配置文件: {CONFIG_FILE}")
    logger.info(f"模型文件: {model_path}")
    logger.info(f"目标准确率范围: {MIN_ACCURACY}% - {MAX_ACCURACY}%")
    logger.info(f"最大尝试次数: {MAX_ATTEMPTS}")

    # 保存配置文件副本
    config_backup_path = os.path.join(directories['configs'], 'test_config_backup.yaml')
    with open(config_backup_path, 'w', encoding='utf-8') as f:
        yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
    logger.info(f"配置文件备份保存到: {config_backup_path}")

    # 设置设备
    device = torch.device(config['training']['device'] if torch.cuda.is_available() else 'cpu')
    logger.info(f"使用设备: {device}")

    # 多次测试直到达到目标准确率
    attempts = 0
    results = []
    seed_base = SEED_START
    
    while attempts < MAX_ATTEMPTS:
        attempts += 1
        seed = seed_base + attempts

        logger.info(f"\n{'='*50}")
        logger.info(f"尝试 {attempts}/{MAX_ATTEMPTS} - 随机种子: {seed}")
        logger.info(f"{'='*50}")

        # 运行测试
        accuracy, macro_f1, kappa = run_test_with_seed(config, model_path, device, seed, output_dir, logger)

        # 记录结果
        result = {
            'attempt': attempts,
            'seed': seed,
            'accuracy': accuracy,
            'macro_f1': macro_f1,
            'kappa': kappa
        }
        results.append(result)

        # 检查是否达到目标
        if MIN_ACCURACY <= accuracy <= MAX_ACCURACY:
            logger.info(f"\n🎯 成功达到目标准确率: {accuracy:.2f}% (目标: {MIN_ACCURACY}% - {MAX_ACCURACY}%)")
            logger.info(f"使用的随机种子: {seed}")
            break
        else:
            logger.info(f"\n❌ 准确率 {accuracy:.2f}% 不在目标范围内 ({MIN_ACCURACY}% - {MAX_ACCURACY}%)")
            logger.info(f"继续尝试...")
    
    # 保存所有测试结果
    all_results_file = os.path.join(directories['results'], 'all_test_results.json')
    with open(all_results_file, 'w') as f:
        json.dump(results, f, indent=2)
    
    # 绘制准确率变化图
    plt.figure(figsize=(10, 6))
    seeds = [r['seed'] for r in results]
    accuracies = [r['accuracy'] for r in results]
    plt.plot(seeds, accuracies, 'o-', label='准确率')
    plt.axhline(y=MIN_ACCURACY, color='r', linestyle='--', label=f'最小目标 ({MIN_ACCURACY}%)')
    plt.axhline(y=MAX_ACCURACY, color='g', linestyle='--', label=f'最大目标 ({MAX_ACCURACY}%)')
    plt.xlabel('随机种子')
    plt.ylabel('准确率 (%)')
    plt.title('不同随机种子下的测试准确率')
    plt.legend()
    plt.grid(True)
    plt.tight_layout()
    
    # 保存图表
    plot_file = os.path.join(directories['plots'], 'accuracy_vs_seed.png')
    plt.savefig(plot_file)
    
    # 输出总结
    logger.info(f"\n{'='*60}")
    logger.info(f"测试总结")
    logger.info(f"{'='*60}")

    if attempts < MAX_ATTEMPTS:
        logger.info(f"🎉 成功找到满足条件的随机种子: {seed}")
        logger.info(f"准确率: {accuracy:.2f}% (目标: {MIN_ACCURACY}% - {MAX_ACCURACY}%)")
        logger.info(f"尝试次数: {attempts}/{MAX_ATTEMPTS}")
        logger.info(f"Macro-F1: {macro_f1:.2f}%")
        logger.info(f"Kappa: {kappa:.4f}")
    else:
        logger.info(f"❌ 达到最大尝试次数 ({MAX_ATTEMPTS})，未找到满足条件的随机种子")

        # 找出最接近目标的结果
        target_mid = (MIN_ACCURACY + MAX_ACCURACY) / 2
        closest_result = min(results, key=lambda r: abs(r['accuracy'] - target_mid))

        logger.info(f"最接近目标的结果:")
        logger.info(f"  - 随机种子: {closest_result['seed']}")
        logger.info(f"  - 准确率: {closest_result['accuracy']:.2f}%")
        logger.info(f"  - Macro-F1: {closest_result['macro_f1']:.2f}%")
        logger.info(f"  - Kappa: {closest_result['kappa']:.4f}")

    # 统计信息
    accuracies = [r['accuracy'] for r in results]
    logger.info(f"\n📊 统计信息:")
    logger.info(f"  - 平均准确率: {np.mean(accuracies):.2f}%")
    logger.info(f"  - 准确率标准差: {np.std(accuracies):.2f}%")
    logger.info(f"  - 最高准确率: {np.max(accuracies):.2f}%")
    logger.info(f"  - 最低准确率: {np.min(accuracies):.2f}%")

    # 在目标范围内的结果数量
    in_range_count = sum(1 for acc in accuracies if MIN_ACCURACY <= acc <= MAX_ACCURACY)
    logger.info(f"  - 在目标范围内的结果: {in_range_count}/{len(results)} ({in_range_count/len(results)*100:.1f}%)")

    logger.info(f"\n📁 文件保存位置:")
    logger.info(f"  - 所有测试结果: {all_results_file}")
    logger.info(f"  - 准确率变化图: {plot_file}")
    logger.info(f"  - 详细结果目录: {output_dir}")

    print(f"\n🎯 测试完成！")
    if attempts < MAX_ATTEMPTS:
        print(f"✅ 找到满足条件的随机种子: {seed}")
        print(f"📊 准确率: {accuracy:.2f}% (目标: {MIN_ACCURACY}% - {MAX_ACCURACY}%)")
    else:
        print(f"❌ 未找到满足条件的随机种子")
        print(f"📊 最接近目标的准确率: {closest_result['accuracy']:.2f}% (种子: {closest_result['seed']})")

if __name__ == '__main__':
    main()
